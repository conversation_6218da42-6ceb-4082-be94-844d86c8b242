from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import H<PERSON><PERSON><PERSON>ponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import requests
import json
import uvicorn
import os
from msal import ConfidentialClientApplication

app = Fast<PERSON>I(title="AJAIA CRM Intelligence", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variable to store the bearer token
bearer_token = None

# Dynamics 365 Configuration - Using Delegated Permissions with Client Credentials
DYNAMICS_CLIENT_ID = 'e459a027-c553-4fae-aeee-b920fe0de1d5'
DYNAMICS_CLIENT_SECRET = '****************************************'  # Replace with your actual client secret
DYNAMICS_TENANT_ID = '1e31133f-09d5-4786-b0bf-5771ddbd32d2'
DYNAMICS_RESOURCE = 'https://champlain.crm.dynamics.com'
DYNAMICS_AUTHORITY = f'https://login.microsoftonline.com/{DYNAMICS_TENANT_ID}'
DYNAMICS_SCOPE = [f'{DYNAMICS_RESOURCE}/.default']  # Use .default for admin-consented permissions
DYNAMICS_API_URL = f'{DYNAMICS_RESOURCE}/api/data/v9.2/contacts'


# Pydantic models for request/response validation
class AuthRequest(BaseModel):
    username: str
    apikey: str


class SearchRequest(BaseModel):
    query: str


class ContactRequest(BaseModel):
    firstname: str
    lastname: str
    email: str | None = None  # Made optional with default None
    phone: str | None = None
    job_title: str | None = None
    company_name: str  # Company to link the contact to


class ApiResponse(BaseModel):
    success: bool
    error: str = None
    data: dict = None


# === Dynamics 365 Helper Functions ===
def get_dynamics_access_token():
    """Get access token for Dynamics 365 using Client Credentials with Delegated Permissions"""
    try:
        app = ConfidentialClientApplication(
            DYNAMICS_CLIENT_ID,
            authority=DYNAMICS_AUTHORITY,
            client_credential=DYNAMICS_CLIENT_SECRET
        )

        print(f"Attempting to acquire token for client: {DYNAMICS_CLIENT_ID}")
        print(f"Authority: {DYNAMICS_AUTHORITY}")
        print(f"Scope: {DYNAMICS_SCOPE}")

        # Use client credentials flow with admin-consented delegated permissions
        token_response = app.acquire_token_for_client(scopes=DYNAMICS_SCOPE)

        print(f"Token response: {token_response}")

        if 'access_token' in token_response:
            access_token = token_response['access_token']
            print(f"✅ Successfully acquired access token")
            print(f"Token starts with: {access_token[:50]}...")
            return access_token
        else:
            error = token_response.get('error', 'Unknown error')
            error_description = token_response.get('error_description', 'No description')
            error_msg = f"Error: {error} - {error_description}"
            print(f"❌ Failed to acquire token: {error_msg}")
            raise Exception(f"Failed to acquire access token: {error_msg}")

    except Exception as e:
        print(f"❌ Exception in get_dynamics_access_token: {str(e)}")
        raise Exception(f"Authentication error: {str(e)}")

    return access_token


def find_account_by_name(account_name, headers):
    """Find account ID by company name"""
    search_url = f'{DYNAMICS_RESOURCE}/api/data/v9.2/accounts?$filter=name eq \'{account_name}\''

    try:
        search_response = requests.get(search_url, headers=headers)
        search_response.raise_for_status()

        accounts = search_response.json()['value']
        if accounts:
            return accounts[0]['accountid']
        else:
            return None
    except requests.exceptions.RequestException as e:
        print(f"Error searching for account: {e}")
        return None


def create_contact_for_company(contact_info, company_name):
    """
    Create a contact in Dynamics 365 linked to a specific company

    Args:
        contact_info (dict): Contact details with keys: firstname, lastname, email, phone, job_title
        company_name (str): Name of the company to link the contact to

    Returns:
        dict: Result with success/error information
    """
    try:
        # Get access token
        access_token = get_dynamics_access_token()

        # Set up headers
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0',
            'Accept': 'application/json'
        }

        # Find the account first
        account_id = find_account_by_name(company_name, headers)

        if not account_id:
            return {
                "success": False,
                "error": f"Company '{company_name}' not found in Dynamics"
            }

        # Prepare contact data
        contact_data = {
            "firstname": contact_info.get("firstname"),
            "lastname": contact_info.get("lastname"),
            "<EMAIL>": f"/accounts({account_id})"
        }

        # Add email - always include field (empty string if no email provided)
        if contact_info.get("email"):
            contact_data["emailaddress1"] = contact_info.get("email")
        else:
            contact_data["emailaddress1"] = ""  # Send empty string when no email

        # Add phone if provided - try the most common field names
        if contact_info.get("phone"):
            # Most common Dynamics 365 phone field names
            contact_data["chm_workphone"] = contact_info.get("phone")  # Business Phone 1
            # Mobile Phone
            # Work Phone (alternative)

        # Add job title if provided
        if contact_info.get("job_title"):
            contact_data["jobtitle"] = contact_info.get("job_title")

        # Remove None values
        contact_data = {k: v for k, v in contact_data.items() if v is not None}

        print(f"Creating contact with data: {contact_data}")
        print(f"Phone value being sent: {contact_info.get('phone')}")
        print(f"Raw contact_info: {contact_info}")

        # Create the contact
        response = requests.post(DYNAMICS_API_URL, headers=headers, data=json.dumps(contact_data))

        # Handle response
        if response.status_code == 204:
            return {
                "success": True,
                "message": "Contact created successfully",
                "account_id": account_id
            }
        elif response.status_code == 201:
            return {
                "success": True,
                "message": "Contact created successfully",
                "account_id": account_id,
                "contact_data": response.json()
            }
        else:
            print(f"ERROR: Failed to create contact. Status: {response.status_code}")
            print(f"ERROR Response: {response.text}")
            return {
                "success": False,
                "error": f"Failed to create contact. Status: {response.status_code}",
                "details": response.text
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"An error occurred: {str(e)}"
        }


# === Existing Endpoints ===

@app.get("/", response_class=HTMLResponse)
async def index():
    """Serve the main HTML page"""
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="HTML file not found")


@app.post("/api/authenticate")
async def authenticate(auth_data: AuthRequest):
    """Authenticate with Preqin API"""
    global bearer_token

    if not auth_data.username or not auth_data.apikey:
        raise HTTPException(status_code=400, detail="Username and API key required")

    try:
        # Authenticate with Preqin API
        auth_payload = {
            'username': auth_data.username,
            'apikey': auth_data.apikey
        }

        response = requests.post(
            'https://api.preqin.com/connect/token',
            data=auth_payload,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )

        print(f"Auth response status: {response.status_code}")
        print(f"Auth response: {response.text}")

        if response.status_code == 200:
            token_data = response.json()
            bearer_token = token_data.get('access_token')
            print(f"Bearer token obtained: {bearer_token[:20]}..." if bearer_token else "No token received")
            return {"success": True}
        else:
            print(f"Auth failed with status {response.status_code}: {response.text}")
            raise HTTPException(status_code=401, detail="Authentication failed")

    except requests.RequestException as e:
        print(f"Authentication exception: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")


@app.post("/api/search")
async def search(search_data: SearchRequest):
    """Search for companies using Preqin API"""
    global bearer_token

    if not bearer_token:
        raise HTTPException(status_code=401, detail="Not authenticated")

    if not search_data.query:
        raise HTTPException(status_code=400, detail="Query required")

    print(f"Searching for: {search_data.query}")

    headers = {
        'Authorization': f'Bearer {bearer_token}',
        'Content-Type': 'application/json'
    }

    try:
        # Search Fund Managers
        print("Searching Fund Managers...")
        fm_response = requests.get(
            'https://api.preqin.com/api/fundmanager',
            headers=headers,
            params={
                'fundManagerName': search_data.query,
                'InactiveFundManagers': '2',
                'include': 'pe,pd,re,inf,nr,hf'
            }
        )

        print(f"Fund Manager response status: {fm_response.status_code}")
        print(f"Fund Manager response: {fm_response.text[:500]}...")

        fund_managers = []
        if fm_response.status_code == 200:
            fm_data = fm_response.json()
            if isinstance(fm_data, dict) and 'data' in fm_data:
                fund_managers = fm_data['data'] if isinstance(fm_data['data'], list) else [fm_data['data']]
            else:
                fund_managers = fm_data if isinstance(fm_data, list) else [fm_data]
            print(f"Found {len(fund_managers)} fund managers")

        # Search Investors
        print("Searching Investors...")
        inv_response = requests.get(
            'https://api.preqin.com/api/Investor',
            headers=headers,
            params={
                'FirmName': search_data.query,
                'InactiveInvestors': '2',
                'include': 'pe,pd,re,inf,nr,hf'
            }
        )

        print(f"Investor response status: {inv_response.status_code}")
        print(f"Investor response: {inv_response.text[:500]}...")

        investors = []
        if inv_response.status_code == 200:
            inv_data = inv_response.json()
            if isinstance(inv_data, dict) and 'data' in inv_data:
                investors = inv_data['data'] if isinstance(inv_data['data'], list) else [inv_data['data']]
            else:
                investors = inv_data if isinstance(inv_data, list) else [inv_data]
            print(f"Found {len(investors)} investors")

        # Get the first company found
        company = fund_managers[0] if fund_managers else (investors[0] if investors else None)

        if not company:
            print("No companies found!")
            raise HTTPException(status_code=404, detail="No companies found")

        print(f"Using company: {company}")

        # DEBUG: Print the entire company object
        print("=== FULL COMPANY DATA ===")
        for key, value in company.items():
            print(f"{key}: {value}")
        print("=== END COMPANY DATA ===")

        # Get firm ID for contact and address lookup
        firm_id = company.get('firmID') or company.get('FirmID') or company.get('fundManagerId')
        print(f"Firm ID: {firm_id}")

        contacts = []
        addresses = []

        if firm_id:
            # Get contacts
            print("Getting contacts...")
            try:
                contacts_response = requests.get(
                    'https://api.preqin.com/api/fundmanager/contact',
                    headers=headers,
                    params={'firmId': firm_id}
                )
                print(f"Contacts response status: {contacts_response.status_code}")
                print(f"Contacts response: {contacts_response.text[:300]}...")

                if contacts_response.status_code == 200:
                    contacts_data = contacts_response.json()
                    if isinstance(contacts_data, dict) and 'data' in contacts_data:
                        contacts = contacts_data['data'] if isinstance(contacts_data['data'], list) else [
                            contacts_data['data']]
                    else:
                        contacts = contacts_data if isinstance(contacts_data, list) else [contacts_data]
                    print(f"Found {len(contacts)} contacts")

                    if contacts:
                        print("=== FIRST CONTACT DATA ===")
                        for key, value in contacts[0].items():
                            print(f"{key}: {value}")
                        print("=== END CONTACT DATA ===")
            except Exception as e:
                print(f"Error getting fund manager contacts: {e}")
                # Try investor contacts if fund manager contacts fail
                try:
                    inv_contacts_response = requests.get(
                        'https://api.preqin.com/api/Investor/contact',
                        headers=headers,
                        params={'firmId': firm_id}
                    )
                    print(f"Investor contacts response status: {inv_contacts_response.status_code}")
                    if inv_contacts_response.status_code == 200:
                        inv_contacts_data = inv_contacts_response.json()
                        if isinstance(inv_contacts_data, dict) and 'data' in inv_contacts_data:
                            contacts = inv_contacts_data['data'] if isinstance(inv_contacts_data['data'], list) else [
                                inv_contacts_data['data']]
                        else:
                            contacts = inv_contacts_data if isinstance(inv_contacts_data, list) else [inv_contacts_data]
                        print(f"Found {len(contacts)} investor contacts")
                except Exception as e2:
                    print(f"Error getting investor contacts: {e2}")

            # Get addresses
            print("Getting addresses...")
            try:
                address_response = requests.get(
                    'https://api.preqin.com/api/fundmanager/address',
                    headers=headers,
                    params={'firmId': firm_id}
                )
                print(f"Address response status: {address_response.status_code}")
                print(f"Address response: {address_response.text[:300]}...")

                if address_response.status_code == 200:
                    address_data = address_response.json()
                    if isinstance(address_data, dict) and 'data' in address_data:
                        addresses = address_data['data'] if isinstance(address_data['data'], list) else [
                            address_data['data']]
                    else:
                        addresses = address_data if isinstance(address_data, list) else [address_data]
                    print(f"Found {len(addresses)} addresses")
            except Exception as e:
                print(f"Error getting fund manager addresses: {e}")
                try:
                    inv_address_response = requests.get(
                        'https://api.preqin.com/api/Investor/address',
                        headers=headers,
                        params={'firmId': firm_id}
                    )
                    print(f"Investor address response status: {inv_address_response.status_code}")
                    if inv_address_response.status_code == 200:
                        inv_address_data = inv_address_response.json()
                        if isinstance(inv_address_data, dict) and 'data' in inv_address_data:
                            addresses = inv_address_data['data'] if isinstance(inv_address_data['data'], list) else [
                                inv_address_data['data']]
                        else:
                            addresses = inv_address_data if isinstance(inv_address_data, list) else [inv_address_data]
                        print(f"Found {len(addresses)} investor addresses")
                except Exception as e2:
                    print(f"Error getting investor addresses: {e2}")

        # Ensure we have exactly 5 contacts
        while len(contacts) < 5:
            contacts.append({
                'contactId': len(contacts),
                'contactName': 'N/A',
                'jobTitle': 'N/A',
                'email': 'N/A',
                'tel': 'N/A',
                'city': 'N/A'
            })

        # Transform data for frontend
        transformed_data = {
            'company': {
                'name': company.get('firmName') or company.get('FirmName') or company.get(
                    'fundManagerName') or 'Unknown Company',
                'type': company.get('firmType') or company.get('Type') or company.get(
                    'InvestorType') or 'Investment Firm',
                'location': company.get('city') or company.get('Location') or 'Not specified',
                'founded': company.get('yearEst') or company.get('Founded') or company.get(
                    'YearFounded') or 'Not specified',
                'aum': company.get('aumCurrMn') or company.get('AUM') or company.get(
                    'AssetsUnderManagement') or 'Not disclosed',
                'employees': company.get('Employees') or 'Not specified',
                'description': company.get('about') or company.get('About') or company.get(
                    'Description') or 'No description available',
                'website': company.get('website') or company.get('Website'),

                # Additional fields from the API response
                'address': company.get('address'),
                'city_full': company.get('city'),
                'state': company.get('stateCounty'),
                'zipCode': company.get('zipCode'),
                'country': company.get('country'),
                'region': company.get('region'),
                'phone': company.get('tel'),
                'email': company.get('email'),
                'generalConsultant': company.get('generalConsultant'),
                'secondaryLocations': company.get('secondaryLocations'),

                # Financial metrics
                'matchingFunds': company.get('matchingFunds'),
                'aumUSDMn': company.get('aumUSDMn'),
                'aumEURMn': company.get('aumEURMn'),

                # Asset allocation data
                'allocationFixedIncomePct': company.get('allocationFixedIncomePct'),
                'allocationFixedIncomeCurrMn': company.get('allocationFixedIncomeCurrMn'),
                'allocationOtherPct': company.get('allocationOtherPct'),
                'allocationOtherCurrMn': company.get('allocationOtherCurrMn'),
            },
            'contacts': [
                {
                    'id': contact.get('contactId', i),
                    'name': contact.get('contactName') or contact.get('Name') or 'N/A',
                    'title': contact.get('jobTitle') or contact.get('Title') or contact.get('Position') or 'N/A',
                    'department': contact.get('Department') or 'N/A',
                    'email': contact.get('email') or contact.get('Email') or 'N/A',
                    'phone': contact.get('tel') or contact.get('Phone') or contact.get('PhoneNumber') or 'N/A',
                    'location': contact.get('city') or contact.get('Location') or 'N/A',
                    'firstName': contact.get('firstName') or 'N/A',
                    'lastName': contact.get('lastName') or 'N/A',
                    'titlePrefix': contact.get('title') or 'N/A',
                    'assetClass': contact.get('assetClass') or 'N/A',
                    'linkedIn': contact.get('linkedIn') or 'N/A',
                    'state': contact.get('state') or 'N/A',
                    'country': contact.get('country') or 'N/A',
                    'zipCode': contact.get('zipCode') or 'N/A',
                    'firmType': contact.get('firmType') or 'N/A',
                    'firmName': contact.get('firmName') or 'N/A',
                    'localLanguageName': contact.get('localLanguageName') or 'N/A'
                }
                for i, contact in enumerate(contacts[:5])
            ],
            'addresses': addresses,
            'account': {
                'relationship_status': 'API Data Available',
                'account_manager': 'Preqin Platform',
                'last_interaction': '2024-07-02',
                'total_investments': company.get('TotalInvestments') or 'Not disclosed',
                'active_funds': company.get('ActiveFunds') or 'Not specified',
                'investment_focus': company.get('AssetClasses') or company.get('Strategies') or ['Not specified'],
                'performance_metrics': {
                    'irr': company.get('IRR') or 'N/A',
                    'moic': company.get('MOIC') or 'N/A',
                    'dpi': company.get('DPI') or 'N/A'
                }
            }
        }

        print(f"Returning transformed data for company: {transformed_data['company']['name']}")
        return {"success": True, "data": transformed_data}

    except requests.RequestException as e:
        print(f"Search exception: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


# === NEW ENDPOINT TO FETCH CRM CONTACTS ===
@app.post("/api/dynamics/get-contacts")
async def get_dynamics_contacts(request: dict):
    """
    Get existing contacts from Dynamics 365 for a specific company

    Request body:
    {
        "company_name": "1st Source Bank"
    }
    """
    try:
        company_name = request.get('company_name')
        if not company_name:
            raise HTTPException(status_code=400, detail="Company name is required")

        # Get access token
        access_token = get_dynamics_access_token()

        # Set up headers
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0',
            'Accept': 'application/json'
        }

        # Find the account first
        account_id = find_account_by_name(company_name, headers)

        if not account_id:
            return {
                "success": True,
                "contacts": [],
                "message": f"Company '{company_name}' not found in Dynamics"
            }

        # Get contacts for this account - UPDATED: Added chm_personalmobile field and removed contactid
        contacts_url = f'{DYNAMICS_RESOURCE}/api/data/v9.2/contacts?$filter=_parentcustomerid_value eq {account_id}&$select=firstname,lastname,jobtitle,emailaddress1,chm_workphone,chm_personalmobile,salutation,middlename,suffix,description'

        print(f"Fetching contacts for account ID: {account_id}")
        print(f"Contacts URL: {contacts_url}")

        contacts_response = requests.get(contacts_url, headers=headers)

        if contacts_response.status_code == 200:
            contacts_data = contacts_response.json()
            contacts = contacts_data.get('value', [])

            print(f"Found {len(contacts)} contacts for {company_name}")

            # Transform contacts to match frontend format
            transformed_contacts = []
            for i, contact in enumerate(contacts):
                transformed_contact = {
                    'firstName': contact.get('firstname', 'N/A'),
                    'lastName': contact.get('lastname', 'N/A'),
                    'title': contact.get('jobtitle', 'N/A'),
                    'phone': contact.get('chm_workphone', 'N/A'),
                    'mobile': contact.get('chm_personalmobile', 'N/A'),  # UPDATED: Use chm_personalmobile
                    'email': contact.get('emailaddress1', 'N/A'),
                    'titlePrefix': contact.get('salutation', 'N/A'),
                    'middleName': contact.get('middlename', 'N/A'),
                    'suffix': contact.get('suffix', 'N/A'),
                    'dear': contact.get('firstname', 'N/A'),
                    'salutation': contact.get('salutation', 'N/A'),  # Just use the raw salutation field
                    'description': contact.get('description', 'N/A'),
                    # Set defaults for other fields (no asset class or linkedin for CRM)
                    'assetClass': 'N/A',  # CRM doesn't have asset classes
                    'location': 'N/A',
                    'state': 'N/A',
                    'zipCode': 'N/A',
                    'country': 'N/A',
                    'linkedIn': 'N/A',  # CRM doesn't have LinkedIn
                    'firmType': 'N/A',
                    'firmName': company_name
                }
                transformed_contacts.append(transformed_contact)

            return {
                "success": True,
                "contacts": transformed_contacts,
                "account_id": account_id,
                "company_name": company_name
            }
        else:
            print(f"Error fetching contacts. Status: {contacts_response.status_code}")
            print(f"Error response: {contacts_response.text}")
            return {
                "success": False,
                "error": f"Failed to fetch contacts. Status: {contacts_response.status_code}",
                "details": contacts_response.text
            }

    except Exception as e:
        print(f"Error fetching CRM contacts: {str(e)}")
        return {
            "success": False,
            "error": f"An error occurred: {str(e)}"
        }


# === SIMPLE FIELD DISCOVERY ===
@app.get("/api/dynamics/sample-contact")
async def get_sample_contact():
    """
    Get a sample contact to see field names
    """
    try:
        # Get access token
        access_token = get_dynamics_access_token()

        # Set up headers
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0',
            'Accept': 'application/json'
        }

        # Get first contact with phone data - UPDATED: Added chm_personalmobile
        contacts_url = f'{DYNAMICS_RESOURCE}/api/data/v9.2/contacts?$top=5&$select=firstname,lastname,telephone1,telephone2,telephone3,chm_personalmobile,homephone,businessphone,chm_workphone,emailaddress1'

        response = requests.get(contacts_url, headers=headers)

        if response.status_code == 200:
            contacts = response.json()
            return {
                "success": True,
                "contacts": contacts.get('value', [])
            }
        else:
            return {
                "success": False,
                "error": f"Failed to get contacts. Status: {response.status_code}",
                "details": response.text
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"An error occurred: {str(e)}"
        }


@app.post("/api/dynamics/create-contact")
async def create_dynamics_contact(contact_data: ContactRequest):
    """
    Create a contact in Dynamics 365 CRM

    Request body:
    {
        "firstname": "John",
        "lastname": "Doe",
        "email": "<EMAIL>",  // optional
        "phone": "************",  // optional
        "job_title": "Managing Director",  // optional
        "company_name": "1st Source Bank"
    }
    """
    try:
        # Validate required fields - only name and company are required now
        if not contact_data.firstname or not contact_data.lastname:
            raise HTTPException(status_code=400, detail="First name and last name are required")

        if not contact_data.company_name:
            raise HTTPException(status_code=400, detail="Company name is required")

        # Email is now optional - no validation needed

        # Prepare contact info for Dynamics function
        contact_info = {
            "firstname": contact_data.firstname,
            "lastname": contact_data.lastname,
            "email": contact_data.email,
            "phone": contact_data.phone,
            "job_title": contact_data.job_title
        }

        print(
            f"Creating contact {contact_data.firstname} {contact_data.lastname} for company {contact_data.company_name}")
        print(f"Contact details: {contact_info}")

        # Create contact in Dynamics 365
        result = create_contact_for_company(contact_info, contact_data.company_name)

        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "data": {
                    "contact": contact_info,
                    "company": contact_data.company_name,
                    "account_id": result.get("account_id")
                }
            }
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create contact: {result['error']}"
            )

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error creating contact: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "AJAIA CRM Intelligence"}


# Run the application
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,  # Enable auto-reload for development
        log_level="info"
    )