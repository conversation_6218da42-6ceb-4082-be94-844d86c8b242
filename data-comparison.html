<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Comparison - AJAIA CRM Intelligence</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden { display: none; }
        .table-cell-min { min-width: 120px; }
        .gradient-bg { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .glass-morphism { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.95); }
        .shadow-soft { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03); }
        .hover-lift { transition: all 0.2s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1); }
        .data-row:hover { background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%); }
        .match-exact { background-color: #dcfce7; border-color: #16a34a; }
        .match-mismatch { background-color: #fef2f2; border-color: #dc2626; }
        .match-partial { background-color: #fef3c7; border-color: #d97706; }
        .comparison-table { overflow-x: auto; }
        @media (max-width: 768px) {
            .comparison-table table { font-size: 0.875rem; }
            .comparison-table th, .comparison-table td { padding: 0.5rem; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
    <div class="min-h-screen p-4">
        <div class="max-w-[95%] mx-auto">
            <!-- Header -->
            <div class="text-center mb-8 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-600 rounded-full mb-4 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                    Data Comparison Report
                </h1>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                    Comprehensive comparison between Preqin and CRM data
                </p>
            </div>

            <!-- Back Button -->
            <div class="mb-6">
                <button onclick="window.close()" 
                        class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-all duration-200 shadow-lg hover-lift">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Close Window
                </button>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-16 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
                    <div class="loading-spinner"></div>
                </div>
                <p class="text-lg text-gray-600 font-medium">Loading comparison data...</p>
                <p class="text-sm text-gray-500 mt-2">Preparing data comparison table</p>
            </div>

            <!-- Comparison Results -->
            <div id="comparisonResults" class="hidden animate-fade-in">
                <!-- Legend -->
                <div class="mb-6 glass-morphism rounded-xl shadow-soft border border-white/20 p-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Legend</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-200 border border-green-500 rounded mr-2"></div>
                            <span class="text-gray-700">Exact Match</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-red-200 border border-red-500 rounded mr-2"></div>
                            <span class="text-gray-700">Mismatch</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gray-200 border border-gray-400 rounded mr-2"></div>
                            <span class="text-gray-700">No CRM Data</span>
                        </div>
                    </div>
                </div>

                <!-- Company Profile Comparison Table -->
                <div class="glass-morphism rounded-xl shadow-soft border border-white/20 overflow-hidden">
                    <div class="gradient-bg px-6 py-4 text-white">
                        <h2 class="text-2xl font-bold">Company Profile Comparison</h2>
                        <p class="text-blue-100 mt-1">Detailed field-by-field comparison</p>
                    </div>
                    
                    <div class="comparison-table overflow-x-auto">
                        <table class="w-full" id="comparisonTable">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b">Field</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b">Preqin Data</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-b">CRM Data</th>
                                    <th class="px-4 py-3 text-center text-sm font-semibold text-gray-900 border-b">Status</th>
                                </tr>
                            </thead>
                            <tbody id="comparisonTableBody">
                                <!-- Table rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="glass-morphism rounded-xl shadow-soft border border-white/20 p-6 text-center">
                        <div class="text-3xl font-bold text-green-600" id="exactMatches">0</div>
                        <div class="text-sm text-gray-600 mt-1">Exact Matches</div>
                    </div>
                    <div class="glass-morphism rounded-xl shadow-soft border border-white/20 p-6 text-center">
                        <div class="text-3xl font-bold text-red-600" id="mismatches">0</div>
                        <div class="text-sm text-gray-600 mt-1">Mismatches</div>
                    </div>
                    <div class="glass-morphism rounded-xl shadow-soft border border-white/20 p-6 text-center">
                        <div class="text-3xl font-bold text-gray-600" id="noCrmData">0</div>
                        <div class="text-sm text-gray-600 mt-1">No CRM Data</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let companyData = null;
        let crmData = null;
        let contactsData = null;

        // Load data from localStorage
        function loadComparisonData() {
            try {
                const storedData = localStorage.getItem('comparisonData');
                if (storedData) {
                    const data = JSON.parse(storedData);
                    console.log('Loaded comparison data:', data);

                    // Check if data is recent (within last 5 minutes)
                    const dataAge = Date.now() - (data.timestamp || 0);
                    if (dataAge > 5 * 60 * 1000) {
                        throw new Error('Comparison data is too old');
                    }

                    companyData = data.companyData;
                    crmData = data.crmData || {};
                    contactsData = data.contactsData || [];

                    // Hide loading and show results
                    document.getElementById('loadingState').classList.add('hidden');
                    document.getElementById('comparisonResults').classList.remove('hidden');

                    // Generate comparison table
                    generateComparisonTable();

                    // Clear the data from localStorage after use
                    localStorage.removeItem('comparisonData');
                } else {
                    throw new Error('No comparison data found');
                }
            } catch (error) {
                console.error('Error loading comparison data:', error);
                showNoDataError();
            }
        }

        function showNoDataError() {
            document.getElementById('loadingState').innerHTML = `
                <div class="text-center py-16">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-lg text-red-600 font-medium">No data available for comparison</p>
                    <p class="text-sm text-gray-500 mt-2">Please search for a company first in the main window</p>
                </div>
            `;
        }

        function normalizeValue(val) {
            if (!val || val === 'N/A' || val === 'Not specified' || val === 'Not disclosed') return '';
            return String(val).toLowerCase().trim();
        }

        function compareValues(preqinValue, crmValue) {
            const preqinNorm = normalizeValue(preqinValue);
            const crmNorm = normalizeValue(crmValue);
            
            // If both are empty, consider them matching
            if (!preqinNorm && !crmNorm) return 'no-data';
            
            // If CRM has no data, mark as no CRM data
            if (!crmNorm) return 'no-crm-data';
            
            // If Preqin has no data but CRM does
            if (!preqinNorm) return 'mismatch';
            
            // Check for exact match
            if (preqinNorm === crmNorm) return 'exact';
            
            return 'mismatch';
        }

        function generateComparisonTable() {
            if (!companyData) return;

            const comparisonFields = [
                { label: 'Company Name', preqinValue: companyData.name, crmField: 'companyName' },
                { label: 'Type', preqinValue: companyData.type, crmField: 'type' },
                { label: 'Subtype', preqinValue: companyData.type, crmField: 'subType' },
                { label: 'AUM', preqinValue: companyData.aum, crmField: 'aum' },
                { label: 'Phone', preqinValue: companyData.phone || 'N/A', crmField: 'phone' },
                { label: 'Address', preqinValue: companyData.address || 'N/A', crmField: 'plus_address1' },
                { label: 'City', preqinValue: companyData.city_full || 'N/A', crmField: 'city' },
                { label: 'State', preqinValue: companyData.state || 'N/A', crmField: 'state' },
                { label: 'Postal Code', preqinValue: companyData.zipCode || 'N/A', crmField: 'postalCode' },
                { label: 'Country', preqinValue: companyData.country || 'N/A', crmField: 'country' },
                { label: 'Region', preqinValue: companyData.region || 'N/A', crmField: 'region' },
                { label: 'Website', preqinValue: companyData.website || 'N/A', crmField: 'website' },
                { label: 'Consultant', preqinValue: companyData.generalConsultant || 'N/A', crmField: 'consultant' },
                { label: 'Rank', preqinValue: 'N/A', crmField: 'rank' },
                { label: 'Direct Bps ($MM)', preqinValue: 'N/A', crmField: 'direct' },
                { label: 'Fund Bps ($MM)', preqinValue: 'N/A', crmField: 'fund' },
                { label: 'Comments', preqinValue: 'N/A', crmField: 'comment' }
            ];

            let exactMatches = 0;
            let mismatches = 0;
            let noCrmDataCount = 0;

            const tableBody = document.getElementById('comparisonTableBody');
            tableBody.innerHTML = '';

            comparisonFields.forEach(field => {
                const crmValue = field.crmField ? crmData[field.crmField] : null;
                const comparison = compareValues(field.preqinValue, crmValue);
                
                let statusClass = '';
                let statusText = '';
                let statusIcon = '';
                
                switch (comparison) {
                    case 'exact':
                        statusClass = 'match-exact';
                        statusText = 'Match';
                        statusIcon = '✓';
                        exactMatches++;
                        break;
                    case 'mismatch':
                        statusClass = 'match-mismatch';
                        statusText = 'Mismatch';
                        statusIcon = '✗';
                        mismatches++;
                        break;
                    case 'no-crm-data':
                    case 'no-data':
                        statusClass = '';
                        statusText = 'No CRM Data';
                        statusIcon = '-';
                        noCrmDataCount++;
                        break;
                }

                const row = document.createElement('tr');
                row.className = `border-b hover:bg-gray-50 ${statusClass}`;
                row.innerHTML = `
                    <td class="px-4 py-3 font-medium text-gray-900">${field.label}</td>
                    <td class="px-4 py-3 text-gray-700">${field.preqinValue || 'N/A'}</td>
                    <td class="px-4 py-3 text-gray-700">${crmValue || 'N/A'}</td>
                    <td class="px-4 py-3 text-center">
                        <span class="inline-flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium ${
                            comparison === 'exact' ? 'bg-green-100 text-green-800' :
                            comparison === 'mismatch' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                        }">
                            ${statusIcon}
                        </span>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // Update summary statistics
            document.getElementById('exactMatches').textContent = exactMatches;
            document.getElementById('mismatches').textContent = mismatches;
            document.getElementById('noCrmData').textContent = noCrmDataCount;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Load comparison data immediately
            loadComparisonData();
        });
    </script>
</body>
</html>
