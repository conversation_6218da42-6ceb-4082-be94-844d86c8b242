<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Comparison - AJAIA CRM Intelligence</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden { display: none; }
        .glass-morphism { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.95); }
        .shadow-soft { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03); }
        .hover-lift { transition: all 0.2s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1); }
        .data-row:hover { background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%); }
        .mismatch-highlight { background-color: rgb(254 242 242); border-color: rgb(252 165 165); }
        .mismatch-text { color: rgb(220 38 38); }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
    <div class="min-h-screen p-4">
        <div class="max-w-[90%] mx-auto">
            <!-- Header -->
            <div class="text-center mb-10 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                    AJAIA CRM Intelligence - Data Comparison
                </h1>
            </div>

            <!-- Back Button -->
            <div class="mb-6">
                <button onclick="window.close()"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-all duration-200 shadow-lg hover-lift">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Close Window
                </button>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-16 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <div class="loading-spinner"></div>
                </div>
                <p class="text-lg text-gray-600 font-medium">Loading comparison data...</p>
                <p class="text-sm text-gray-500 mt-2">Preparing data comparison view</p>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="hidden animate-fade-in">
                <div class="glass-morphism rounded-2xl shadow-soft border border-white/20 overflow-hidden">

                    <!-- Account View Section -->
                    <div class="mb-8">
                        <div class="bg-blue-600 px-6 py-4 text-white">
                            <h2 class="text-xl font-bold">Account View</h2>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                            <!-- CRM Data Section -->
                            <div class="bg-white border-r border-gray-200">
                                <div class="bg-purple-50 border-b border-purple-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-purple-900">CRM Data</h3>
                                </div>
                                <div class="p-6 space-y-1" id="crmAccountDetails">
                                    <!-- CRM account details will be populated here -->
                                </div>
                            </div>

                            <!-- Preqin Data Section -->
                            <div class="bg-white">
                                <div class="bg-green-50 border-b border-green-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-green-900">Preqin Data</h3>
                                </div>
                                <div class="p-6 space-y-1" id="preqinAccountDetails">
                                    <!-- Preqin account details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact View Section -->
                    <div class="border-t border-gray-200">
                        <div class="bg-indigo-600 px-6 py-4 text-white">
                            <h2 class="text-xl font-bold">Contact View</h2>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                            <!-- CRM Contacts Section -->
                            <div class="bg-white border-r border-gray-200">
                                <div class="bg-orange-50 border-b border-orange-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-orange-900">CRM Contacts</h3>
                                </div>
                                <div class="p-6" id="crmContactsContainer">
                                    <p class="text-gray-500 italic">No CRM contacts available</p>
                                </div>
                            </div>

                            <!-- Preqin Contacts Section -->
                            <div class="bg-white">
                                <div class="bg-teal-50 border-b border-teal-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-teal-900">Preqin Contacts</h3>
                                </div>
                                <div class="p-6" id="preqinContactsContainer">
                                    <!-- Preqin contacts will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="px-8 py-4 bg-gray-50 border-t border-gray-100">
                        <div class="flex items-center justify-between text-sm">
                            <div class="text-gray-600">Data sourced from Preqin's live API platform</div>
                            <div class="text-gray-500">Last updated: <span id="lastUpdated"></span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let companyData = null;
        let crmData = null;
        let contactsData = null;

        // Load data from URL parameters
        function loadComparisonData() {
            try {
                // Get data parameter from URL
                const urlParams = new URLSearchParams(window.location.search);
                const dataParam = urlParams.get('data');

                if (!dataParam) {
                    throw new Error('No data parameter found in URL');
                }

                // Decode base64 data
                const decodedData = atob(decodeURIComponent(dataParam));
                const data = JSON.parse(decodedData);

                console.log('Loaded comparison data from URL:', data);

                companyData = data.company;
                crmData = data.crm || {};
                contactsData = data.contacts || [];

                if (!companyData) {
                    throw new Error('No company data found');
                }

                // Store data globally
                window.currentContacts = contactsData;
                window.currentCompany = companyData;
                window.crmData = crmData;

                // Hide loading and show results
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('searchResults').classList.remove('hidden');

                // Display the comparison data
                displayResults();

            } catch (error) {
                console.error('Error loading comparison data:', error);
                showNoDataError();
            }
        }

        function showNoDataError() {
            document.getElementById('loadingState').innerHTML = `
                <div class="text-center py-16">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-lg text-red-600 font-medium">No data available for comparison</p>
                    <p class="text-sm text-gray-500 mt-2">Please search for a company first in the main window</p>
                </div>
            `;
        }

        function toggleAccordion(accordionId) {
            const accordion = document.getElementById(accordionId);
            const chevron = document.getElementById(accordionId + 'Chevron');

            if (accordion.style.maxHeight && accordion.style.maxHeight !== '0px') {
                accordion.style.maxHeight = '0px';
                chevron.style.transform = 'rotate(0deg)';
            } else {
                accordion.style.maxHeight = accordion.scrollHeight + 'px';
                chevron.style.transform = 'rotate(180deg)';
            }
        }

        function compareWithCRM(preqinValue, crmValue) {
            const normalizeValue = (val) => {
                if (!val || val === 'N/A' || val === 'Not specified' || val === 'Not disclosed') return '';
                return String(val).toLowerCase().trim();
            };

            const preqinNorm = normalizeValue(preqinValue);
            const crmNorm = normalizeValue(crmValue);

            if (!preqinNorm && !crmNorm) return { match: true, crmValue: crmValue };
            if (!crmNorm) return { match: true, crmValue: crmValue };

            const exactMatch = preqinNorm === crmNorm;
            return { match: exactMatch, crmValue: crmValue };
        }

        function createAccountSection(data, isCRM = false) {
            const fields = [
                { label: 'Data Source', value: isCRM ? 'CRM' : 'Preqin', field: null, highlight: true },
                { label: 'Company Name', value: data.name || data.companyName || 'N/A', field: 'companyName', highlight: true },
                { label: 'Rank', value: data.rank || 'N/A', field: 'rank' },
                { label: 'Type', value: data.type || 'N/A', field: 'type', highlight: true },
                { label: 'Subtype', value: data.subType || data.type || 'N/A', field: 'subType' },
                { label: 'Direct Bps ($MM)', value: data.direct || 'N/A', field: 'direct' },
                { label: 'Fund Bps ($MM)', value: data.fund || 'N/A', field: 'fund' },
                { label: 'AUM ($B)', value: data.aum || 'N/A', field: 'aum', highlight: true },
                { label: 'Comments', value: data.comment || 'N/A', field: 'comment' },
                { label: 'Main Phone', value: data.phone || 'N/A', field: 'phone', link: data.phone ? `tel:${data.phone}` : null },
                { label: 'Street 1', value: data.address || data.plus_address1 || 'N/A', field: 'plus_address1' },
                { label: 'Street 2', value: data.plus_address2 || 'N/A', field: 'plus_address2' },
                { label: 'Cross Street', value: data.cross || 'N/A', field: 'cross' },
                { label: 'City', value: data.city_full || data.city || 'N/A', field: 'city' },
                { label: 'State', value: data.state || 'N/A', field: 'state' },
                { label: 'Postal Code', value: data.zipCode || data.postalCode || 'N/A', field: 'postalCode' },
                { label: 'Country', value: data.country || 'N/A', field: 'country' },
                { label: 'Region', value: data.region || 'N/A', field: 'region' },
                { label: 'Website', value: data.website || 'N/A', field: 'website', link: data.website },
                { label: 'Consultant', value: data.generalConsultant || data.consultant || 'N/A', field: 'consultant' }
            ];

            return fields.map(item => {
                let valueClass = item.highlight ? 'font-semibold text-gray-900' : 'text-gray-600';
                let rowClass = 'flex justify-between items-center py-3 px-2 border-b border-gray-100 data-row group';

                if (!isCRM && item.field && window.crmData) {
                    const crmValue = window.crmData[item.field];
                    const comparison = compareWithCRM(item.value, crmValue);
                    if (crmValue && !comparison.match) {
                        valueClass = 'font-semibold mismatch-text';
                        rowClass += ' mismatch-highlight';
                    }
                }

                return `
                    <div class="${rowClass}">
                        <span class="font-medium text-gray-700 text-sm">${item.label}</span>
                        <span class="text-right ${valueClass} text-sm">
                            ${item.link ? `<a href="${item.link}" class="text-blue-600 hover:text-blue-800 transition-colors" ${item.link.startsWith('http') ? 'target="_blank" rel="noopener"' : ''}>${item.value}</a>` : item.value}
                        </span>
                    </div>
                `;
            }).join('');
        }

        function createContactDropdown(contact, index, isCRM = false) {
            const assetClasses = ['PE', 'PD', 'RE', 'NR', 'INF'];
            const assetClassColors = {
                'PE': 'bg-blue-100 text-blue-800 border-blue-200',
                'PD': 'bg-green-100 text-green-800 border-green-200',
                'RE': 'bg-purple-100 text-purple-800 border-purple-200',
                'NR': 'bg-orange-100 text-orange-800 border-orange-200',
                'INF': 'bg-indigo-100 text-indigo-800 border-indigo-200'
            };

            let assetClass = 'N/A';
            if (isCRM) {
                assetClass = contact.assetClass || assetClasses[index % assetClasses.length];
            } else {
                if (!contact || contact.assetClass === 'N/A') {
                    assetClass = assetClasses[index] || 'N/A';
                } else if (contact.assetClass.includes(assetClasses[index])) {
                    assetClass = assetClasses[index];
                } else if (index === 0) {
                    assetClass = contact.assetClass;
                } else {
                    assetClass = assetClasses[index] || 'N/A';
                }
            }

            const assetClassColor = assetClassColors[assetClass] || 'bg-gray-100 text-gray-800 border-gray-200';
            const contactName = `${contact.firstName !== 'N/A' ? contact.firstName : ''} ${contact.lastName !== 'N/A' ? contact.lastName : ''}`.trim() || 'Contact';
            const prefix = isCRM ? 'crm' : 'preqin';

            const contactFields = [
                { field: 'Data Source', value: isCRM ? 'CRM' : 'Preqin', highlight: true },
                { field: 'Dear', value: contact.dear || 'N/A' },
                { field: 'Salutation', value: contact.salutation || 'N/A' },
                { field: 'First Name', value: contact.firstName || 'N/A', highlight: true },
                { field: 'Middle Name', value: contact.middleName || 'N/A' },
                { field: 'Last Name', value: contact.lastName || 'N/A', highlight: true },
                { field: 'Suffix', value: contact.suffix || 'N/A' },
                { field: 'Job Title', value: contact.title || 'N/A', highlight: true },
                { field: 'Phone (W)', value: contact.phone || 'N/A', link: true },
                { field: 'Mobile', value: contact.mobile || 'N/A', link: true },
                { field: 'Email (W)', value: contact.email || 'N/A', email: true },
                { field: 'Contact ID', value: contact.id || 'N/A' },
                { field: 'Title/Prefix', value: contact.titlePrefix || 'N/A' },
                { field: 'LinkedIn', value: contact.linkedIn || 'N/A', linkedin: true },
                { field: 'Asset Classes', value: assetClass, badge: true },
                { field: 'City', value: contact.location || 'N/A' },
                { field: 'State', value: contact.state || 'N/A' },
                { field: 'Zip Code', value: contact.zipCode || 'N/A' },
                { field: 'Country', value: contact.country || 'N/A' },
                { field: 'Firm Type', value: contact.firmType || 'N/A' }
            ];

            return `
                <div class="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 mb-4">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 cursor-pointer hover:from-gray-100 hover:to-gray-150 transition-colors duration-200" onclick="toggleAccordion('${prefix}Contact${index}')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900">${contactName}</h4>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-sm text-gray-600">${contact.title !== 'N/A' ? contact.title : 'Contact #' + (index + 1)}</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${assetClassColor} border">
                                            ${assetClass}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <svg id="${prefix}Contact${index}Chevron" class="w-5 h-5 transform transition-transform duration-200 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0" id="${prefix}Contact${index}">
                        <div class="p-4 bg-white">
                            <div class="grid grid-cols-1 gap-3">
                                ${contactFields.map(field => {
                                    const value = field.value;
                                    if (value === 'N/A' && !['LinkedIn', 'Asset Classes'].includes(field.field)) return '';

                                    let cellContent = value;
                                    let valueClass = field.highlight ? 'font-semibold text-gray-900' : 'text-gray-600';

                                    if (field.email && value !== 'N/A') {
                                        cellContent = `<a href="mailto:${value}" class="text-blue-600 hover:text-blue-800 transition-colors font-medium">${value}</a>`;
                                    } else if (field.link && value !== 'N/A') {
                                        cellContent = `<a href="tel:${value}" class="text-blue-600 hover:text-blue-800 transition-colors">${value}</a>`;
                                    } else if (field.linkedin && value !== 'N/A') {
                                        cellContent = `<a href="https://${value}" target="_blank" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                            View Profile
                                        </a>`;
                                    } else if (field.badge && value !== 'N/A') {
                                        const badgeClass = assetClassColors[value] || 'bg-gray-100 text-gray-800 border-gray-200';
                                        cellContent = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeClass} border">${value}</span>`;
                                    }

                                    return `
                                        <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                                            <span class="font-medium text-gray-700 text-sm">${field.field}</span>
                                            <span class="text-right ${valueClass} text-sm">
                                                ${cellContent}
                                            </span>
                                        </div>
                                    `;
                                }).filter(Boolean).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function displayResults() {
            // Update timestamp
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();

            // Populate CRM Account Details (empty for now as requested)
            document.getElementById('crmAccountDetails').innerHTML = createAccountSection({}, true);

            // Populate Preqin Account Details
            document.getElementById('preqinAccountDetails').innerHTML = createAccountSection(companyData, false);

            // CRM Contacts (empty for now as requested)
            document.getElementById('crmContactsContainer').innerHTML = '<p class="text-gray-500 italic">No CRM contacts available</p>';

            // Populate Preqin Contacts
            const preqinContactsHTML = contactsData.map((contact, index) =>
                createContactDropdown(contact, index, false)
            ).join('');
            document.getElementById('preqinContactsContainer').innerHTML = preqinContactsHTML || '<p class="text-gray-500 italic">No Preqin contacts available</p>';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Load comparison data immediately
            loadComparisonData();
        });
    </script>
</body>
</html>
