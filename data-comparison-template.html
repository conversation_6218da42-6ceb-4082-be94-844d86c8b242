<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preqin CRM - Company Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden { display: none; }
        .glass-morphism { backdrop-filter: blur(16px); background: rgba(255, 255, 255, 0.95); }
        .shadow-soft { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03); }
        .hover-lift { transition: all 0.2s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1); }
        .data-row:hover { background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%); }
        .mismatch-highlight { background-color: rgb(254 242 242); border-color: rgb(252 165 165); }
        .mismatch-text { color: rgb(220 38 38); }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
    <div class="min-h-screen p-4">
        <div class="max-w-[90%] mx-auto">
            <!-- Header -->
            <div class="text-center mb-10 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                    AJAIA CRM Intelligence
                </h1>

            </div>

            <!-- API Credentials Form -->
            <div id="credentialsForm" class="max-w-lg mx-auto mb-10 glass-morphism rounded-2xl shadow-soft border border-white/20 p-8 animate-slide-up">
                <div class="flex items-center mb-6">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">API Authentication</h3>
                </div>
                <div class="space-y-5">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Username</label>
                        <input type="text" id="username" placeholder="Enter your Preqin username"
                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 bg-white/70">
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">API Key</label>
                        <input type="password" id="apikey" placeholder="Enter your API key"
                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 bg-white/70">
                    </div>
                    <button onclick="saveCredentials()"
                            class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-4 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 font-semibold shadow-lg hover-lift">
                        Connect to Preqin
                    </button>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="mb-10">
                <div class="max-w-3xl mx-auto relative">
                    <div class="relative glass-morphism rounded-2xl shadow-soft border border-white/20 p-1 hover-lift">
                        <input type="text" id="searchQuery" placeholder="Search companies (e.g., BlackRock, KKR, Apollo)"
                               class="w-full px-6 py-4 pl-14 pr-48 border-0 rounded-xl focus:ring-2 focus:ring-blue-500 outline-none text-lg bg-transparent placeholder-gray-500"
                               onkeypress="handleKeyPress(event)">
                        <svg class="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-2">
                            <button onclick="showCredentials()"
                                    class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-all duration-200 text-sm font-medium">
                                Settings
                            </button>
                            <button onclick="handleSearch()" id="searchBtn"
                                    class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold">
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="max-w-2xl mx-auto mb-8 p-4 bg-red-50 border border-red-200 rounded-xl hidden">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-red-700 font-medium" id="errorText"></p>
                </div>
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="max-w-2xl mx-auto mb-8 p-4 bg-green-50 border border-green-200 rounded-xl hidden">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-green-700 font-medium" id="successText"></p>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-16 hidden">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <div class="loading-spinner"></div>
                </div>
                <p class="text-lg text-gray-600 font-medium">Searching Preqin database...</p>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="hidden animate-fade-in">
                <div class="glass-morphism rounded-2xl shadow-soft border border-white/20 overflow-hidden">

                    <!-- Account View Section -->
                    <div class="mb-8">
                        <div class="bg-blue-600 px-6 py-4 text-white">
                            <h2 class="text-xl font-bold">Account View</h2>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                            <!-- CRM Data Section -->
                            <div class="bg-white border-r border-gray-200">
                                <div class="bg-purple-50 border-b border-purple-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-purple-900">CRM Data</h3>
                                </div>
                                <div class="p-6 space-y-1" id="crmAccountDetails">
                                    <!-- CRM account details will be populated here -->
                                </div>
                            </div>

                            <!-- Preqin Data Section -->
                            <div class="bg-white">
                                <div class="bg-green-50 border-b border-green-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-green-900">Preqin Data</h3>
                                </div>
                                <div class="p-6 space-y-1" id="preqinAccountDetails">
                                    <!-- Preqin account details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact View Section -->
                    <div class="border-t border-gray-200">
                        <div class="bg-indigo-600 px-6 py-4 text-white">
                            <h2 class="text-xl font-bold">Contact View</h2>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                            <!-- CRM Contacts Section -->
                            <div class="bg-white border-r border-gray-200">
                                <div class="bg-orange-50 border-b border-orange-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-orange-900">CRM Contacts</h3>
                                </div>
                                <div class="p-6" id="crmContactsContainer">
                                    <!-- CRM contacts will be populated here -->
                                </div>
                            </div>

                            <!-- Preqin Contacts Section -->
                            <div class="bg-white">
                                <div class="bg-teal-50 border-b border-teal-100 px-6 py-3">
                                    <h3 class="text-lg font-semibold text-teal-900">Preqin Contacts</h3>
                                </div>
                                <div class="p-6" id="preqinContactsContainer">
                                    <!-- Preqin contacts will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="px-8 py-4 bg-gray-50 border-t border-gray-100">
                        <div class="flex items-center justify-between text-sm">
                            <div class="text-gray-600">Data sourced from Preqin's live API platform</div>
                            <div class="text-gray-500">Last updated: <span id="lastUpdated"></span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isAuthenticated = false;

        function showCredentials() {
            document.getElementById('credentialsForm').classList.remove('hidden');
        }

        function hideCredentials() {
            document.getElementById('credentialsForm').classList.add('hidden');
        }

        function showError(message) {
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').classList.remove('hidden');
            // Hide success message if showing
            document.getElementById('successMessage').classList.add('hidden');
        }

        function hideError() {
            document.getElementById('errorMessage').classList.add('hidden');
        }

        function showSuccess(message) {
            document.getElementById('successText').textContent = message;
            document.getElementById('successMessage').classList.remove('hidden');
            // Hide error message if showing
            document.getElementById('errorMessage').classList.add('hidden');
            // Auto-hide after 5 seconds
            setTimeout(() => {
                document.getElementById('successMessage').classList.add('hidden');
            }, 5000);
        }

        function showLoading() {
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('searchBtn').innerHTML = '<div class="loading-spinner"></div>Searching...';
        }

        function hideLoading() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('searchBtn').disabled = false;
            document.getElementById('searchBtn').textContent = 'Search';
        }

        function toggleAccordion(accordionId) {
            const accordion = document.getElementById(accordionId);
            const chevron = document.getElementById(accordionId + 'Chevron');

            if (accordion.style.maxHeight && accordion.style.maxHeight !== '0px') {
                accordion.style.maxHeight = '0px';
                chevron.style.transform = 'rotate(0deg)';
            } else {
                accordion.style.maxHeight = accordion.scrollHeight + 'px';
                chevron.style.transform = 'rotate(180deg)';
            }
        }

        async function exportContactToCRM(contactIndex) {
            if (!window.currentContacts || !window.currentCompany) {
                showError('No contact data available to export');
                return;
            }

            const contact = window.currentContacts[contactIndex];
            const company = window.currentCompany;

            // Validate required fields - only name is required now (email is optional)
            if (!contact.firstName || contact.firstName === 'N/A' ||
                !contact.lastName || contact.lastName === 'N/A') {
                showError('Contact must have both first name and last name to export');
                return;
            }

            if (!company.name || company.name === 'N/A') {
                showError('Company name is required to export contact');
                return;
            }

            // Prepare the contact data for the API
            const contactData = {
                firstname: contact.firstName,
                lastname: contact.lastName,
                email: contact.email && contact.email !== 'N/A' ? contact.email : null,
                phone: contact.phone && contact.phone !== 'N/A' ? contact.phone : null,
                job_title: contact.title && contact.title !== 'N/A' ? contact.title : null,
                company_name: company.name
            };

            // Update BLUE button to show loading state
            const exportButton = document.getElementById(`exportBtn${contactIndex}`);
            const originalButtonText = exportButton.innerHTML;
            exportButton.disabled = true;
            exportButton.innerHTML = '<div class="loading-spinner"></div>Exporting...';

            try {
                const response = await fetch('http://127.0.0.1:8000/api/dynamics/create-contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(contactData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showSuccess(`Contact ${contact.firstName} ${contact.lastName} successfully exported to Dynamics 365 CRM`);
                    // Update BLUE button to show success state (grey with checkmark)
                    exportButton.innerHTML = '✓ Exported';
                    exportButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    exportButton.classList.add('bg-gray-500');
                    // Keep button disabled to prevent duplicate exports
                } else {
                    throw new Error(result.error || 'Failed to export contact');
                }
            } catch (error) {
                showError(`Failed to export contact: ${error.message}`);
                // Restore BLUE button to original state on error
                exportButton.disabled = false;
                exportButton.innerHTML = originalButtonText;
            }
        }

        async function saveCredentials() {
            const username = document.getElementById('username').value;
            const apikey = document.getElementById('apikey').value;

            if (!username || !apikey) {
                showError('Please enter both username and API key');
                return;
            }

            hideError();
            showLoading();

            try {
                const response = await fetch('http://127.0.0.1:8000/api/authenticate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, apikey })
                });

                const data = await response.json();

                if (data.success) {
                    isAuthenticated = true;
                    hideCredentials();
                    document.getElementById('searchQuery').focus();
                } else {
                    showError(data.error || 'Authentication failed');
                }
            } catch (error) {
                showError('Failed to connect to server');
            } finally {
                hideLoading();
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                handleSearch();
            }
        }

        async function handleSearch() {
            const query = document.getElementById('searchQuery').value.trim();

            if (!query) {
                showError('Please enter a company name');
                return;
            }

            if (!isAuthenticated) {
                showError('Please enter your Preqin API credentials first');
                showCredentials();
                return;
            }

            hideError();
            showLoading();
            document.getElementById('searchResults').classList.add('hidden');

            try {
                const response = await fetch('http://127.0.0.1:8000/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query })
                });

                const result = await response.json();

                if (result.success) {
                    displayResults(result.data);
                } else {
                    showError(result.error || 'Search failed');
                }
            } catch (error) {
                showError('Failed to search companies');
            } finally {
                hideLoading();
            }
        }

        function generateCRMPlaceholderData() {
            return {
                companyName: "Sample Investment Partners LLC",
                rank: "15",
                type: "Private Equity Fund",
                subType: "Growth Capital",
                direct: "250",
                fund: "500",
                aum: "2.5B",
                comment: "High-growth focused firm",
                phone: "******-123-4567",
                plus_address1: "123 Wall Street",
                plus_address2: "Suite 500",
                cross: "Near NYSE",
                city: "New York",
                state: "NY",
                postalCode: "10005",
                country: "United States",
                region: "North America",
                website: "https://sampleinvestment.com",
                consultant: "McKinsey & Company"
            };
        }

        function generateCRMContactPlaceholders() {
            return [
                {
                    firstName: "John",
                    lastName: "Smith",
                    title: "Managing Director",
                    phone: "******-111-2222",
                    mobile: "******-111-3333",
                    email: "<EMAIL>",
                    assetClass: "PE",
                    location: "New York",
                    state: "NY",
                    zipCode: "10005",
                    country: "USA",
                    id: "CRM001",
                    titlePrefix: "Mr.",
                    linkedIn: "linkedin.com/in/johnsmith",
                    firmType: "Private Equity",
                    dear: "John",
                    salutation: "Mr. Smith",
                    middleName: "William",
                    suffix: "Jr."
                },
                {
                    firstName: "Sarah",
                    lastName: "Johnson",
                    title: "Investment Director",
                    phone: "******-333-4444",
                    mobile: "******-333-5555",
                    email: "<EMAIL>",
                    assetClass: "PD",
                    location: "Boston",
                    state: "MA",
                    zipCode: "02101",
                    country: "USA",
                    id: "CRM002",
                    titlePrefix: "Ms.",
                    linkedIn: "linkedin.com/in/sarahjohnson",
                    firmType: "Private Debt",
                    dear: "Sarah",
                    salutation: "Ms. Johnson",
                    middleName: "Elizabeth",
                    suffix: ""
                },
                {
                    firstName: "Michael",
                    lastName: "Chen",
                    title: "Senior Associate",
                    phone: "******-555-6666",
                    mobile: "******-555-7777",
                    email: "<EMAIL>",
                    assetClass: "RE",
                    location: "San Francisco",
                    state: "CA",
                    zipCode: "94105",
                    country: "USA",
                    id: "CRM003",
                    titlePrefix: "Mr.",
                    linkedIn: "linkedin.com/in/michaelchen",
                    firmType: "Real Estate",
                    dear: "Michael",
                    salutation: "Mr. Chen",
                    middleName: "David",
                    suffix: ""
                }
            ];
        }

        function compareWithCRM(preqinValue, crmValue) {
            const normalizeValue = (val) => {
                if (!val || val === 'N/A' || val === 'Not specified' || val === 'Not disclosed') return '';
                return String(val).toLowerCase().trim();
            };

            const preqinNorm = normalizeValue(preqinValue);
            const crmNorm = normalizeValue(crmValue);

            if (!preqinNorm && !crmNorm) return { match: true, crmValue: crmValue };
            if (!crmNorm) return { match: true, crmValue: crmValue };

            const exactMatch = preqinNorm === crmNorm;
            return { match: exactMatch, crmValue: crmValue };
        }

        function createAccountSection(data, isCRM = false) {
            const fields = [
                { label: 'Data Source', value: isCRM ? 'CRM' : 'Preqin', field: null, highlight: true },
                { label: 'Company Name', value: data.name || data.companyName || 'N/A', field: 'companyName', highlight: true },
                { label: 'Rank', value: data.rank || 'N/A', field: 'rank' },
                { label: 'Type', value: data.type || 'N/A', field: 'type', highlight: true },
                { label: 'Subtype', value: data.subType || data.type || 'N/A', field: 'subType' },
                { label: 'Direct Bps ($MM)', value: data.direct || 'N/A', field: 'direct' },
                { label: 'Fund Bps ($MM)', value: data.fund || 'N/A', field: 'fund' },
                { label: 'AUM ($B)', value: data.aum || 'N/A', field: 'aum', highlight: true },
                { label: 'Comments', value: data.comment || 'N/A', field: 'comment' },
                { label: 'Main Phone', value: data.phone || 'N/A', field: 'phone', link: data.phone ? `tel:${data.phone}` : null },
                { label: 'Street 1', value: data.address || data.plus_address1 || 'N/A', field: 'plus_address1' },
                { label: 'Street 2', value: data.plus_address2 || 'N/A', field: 'plus_address2' },
                { label: 'Cross Street', value: data.cross || 'N/A', field: 'cross' },
                { label: 'City', value: data.city_full || data.city || 'N/A', field: 'city' },
                { label: 'State', value: data.state || 'N/A', field: 'state' },
                { label: 'Postal Code', value: data.zipCode || data.postalCode || 'N/A', field: 'postalCode' },
                { label: 'Country', value: data.country || 'N/A', field: 'country' },
                { label: 'Region', value: data.region || 'N/A', field: 'region' },
                { label: 'Website', value: data.website || 'N/A', field: 'website', link: data.website },
                { label: 'Consultant', value: data.generalConsultant || data.consultant || 'N/A', field: 'consultant' }
            ];

            return fields.map(item => {
                let valueClass = item.highlight ? 'font-semibold text-gray-900' : 'text-gray-600';
                let rowClass = 'flex justify-between items-center py-3 px-2 border-b border-gray-100 data-row group';

                if (!isCRM && item.field && window.crmData) {
                    const crmValue = window.crmData[item.field];
                    const comparison = compareWithCRM(item.value, crmValue);
                    if (crmValue && !comparison.match) {
                        valueClass = 'font-semibold mismatch-text';
                        rowClass += ' mismatch-highlight';
                    }
                }

                return `
                    <div class="${rowClass}">
                        <span class="font-medium text-gray-700 text-sm">${item.label}</span>
                        <span class="text-right ${valueClass} text-sm">
                            ${item.link ? `<a href="${item.link}" class="text-blue-600 hover:text-blue-800 transition-colors" ${item.link.startsWith('http') ? 'target="_blank" rel="noopener"' : ''}>${item.value}</a>` : item.value}
                        </span>
                    </div>
                `;
            }).join('');
        }

        function createContactDropdown(contact, index, isCRM = false) {
            const assetClasses = ['PE', 'PD', 'RE', 'NR', 'INF'];
            const assetClassColors = {
                'PE': 'bg-blue-100 text-blue-800 border-blue-200',
                'PD': 'bg-green-100 text-green-800 border-green-200',
                'RE': 'bg-purple-100 text-purple-800 border-purple-200',
                'NR': 'bg-orange-100 text-orange-800 border-orange-200',
                'INF': 'bg-indigo-100 text-indigo-800 border-indigo-200'
            };

            let assetClass = 'N/A';
            if (isCRM) {
                assetClass = contact.assetClass || assetClasses[index % assetClasses.length];
            } else {
                if (!contact || contact.assetClass === 'N/A') {
                    assetClass = assetClasses[index] || 'N/A';
                } else if (contact.assetClass.includes(assetClasses[index])) {
                    assetClass = assetClasses[index];
                } else if (index === 0) {
                    assetClass = contact.assetClass;
                } else {
                    assetClass = assetClasses[index] || 'N/A';
                }
            }

            const assetClassColor = assetClassColors[assetClass] || 'bg-gray-100 text-gray-800 border-gray-200';
            const contactName = `${contact.firstName !== 'N/A' ? contact.firstName : ''} ${contact.lastName !== 'N/A' ? contact.lastName : ''}`.trim() || 'Contact';
            const prefix = isCRM ? 'crm' : 'preqin';

            // Check if contact has required fields for export (only for Preqin contacts)
            const canExport = !isCRM &&
                contact.firstName && contact.firstName !== 'N/A' &&
                contact.lastName && contact.lastName !== 'N/A';

            const contactFields = [
                { field: 'CRM Source', value: isCRM ? 'CRM' : 'Preqin', highlight: true },
                { field: 'Dear', value: contact.dear || 'N/A' },
                { field: 'Salutation', value: contact.salutation || 'N/A' },
                { field: 'First Name', value: contact.firstName || 'N/A', highlight: true },
                { field: 'Middle Name', value: contact.middleName || 'N/A' },
                { field: 'Last Name', value: contact.lastName || 'N/A', highlight: true },
                { field: 'Suffix', value: contact.suffix || 'N/A' },
                { field: 'Job Title', value: contact.title || 'N/A', highlight: true },
                { field: 'Phone (W)', value: contact.phone || 'N/A', link: true },
                { field: 'Mobile', value: contact.mobile || 'N/A', link: true },
                { field: 'Email (W)', value: contact.email || 'N/A', email: true },
                { field: 'Contact ID', value: contact.id || 'N/A' },
                { field: 'Title/Prefix', value: contact.titlePrefix || 'N/A' },
                { field: 'LinkedIn', value: contact.linkedIn || 'N/A', linkedin: true },
                { field: 'Asset Classes', value: assetClass, badge: true },
                { field: 'City', value: contact.location || 'N/A' },
                { field: 'State', value: contact.state || 'N/A' },
                { field: 'Zip Code', value: contact.zipCode || 'N/A' },
                { field: 'Country', value: contact.country || 'N/A' },
                { field: 'Firm Type', value: contact.firmType || 'N/A' }
            ];

            return `
                <div class="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 mb-4">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 cursor-pointer hover:from-gray-100 hover:to-gray-150 transition-colors duration-200" onclick="toggleAccordion('${prefix}Contact${index}')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900">${contactName}</h4>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-sm text-gray-600">${contact.title !== 'N/A' ? contact.title : 'Contact #' + (index + 1)}</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${assetClassColor} border">
                                            ${assetClass}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <svg id="${prefix}Contact${index}Chevron" class="w-5 h-5 transform transition-transform duration-200 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="overflow-hidden transition-all duration-300 ease-in-out max-h-0" id="${prefix}Contact${index}">
                        <div class="p-4 bg-white">
                            ${!isCRM ? `
                                <div class="mb-4 pb-4 border-b border-gray-200 flex justify-end">
                                    <button
                                        id="exportBtn${index}"
                                        onclick="exportContactToCRM(${index})"
                                        class="px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${canExport ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}"
                                        ${!canExport ? 'disabled title="Missing required fields (first name and last name)"' : 'title="Export to Dynamics 365 CRM"'}
                                    >
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                        </svg>
                                        Export
                                    </button>
                                </div>
                            ` : ''}
                            <div class="grid grid-cols-1 gap-3">
                                ${contactFields.map(field => {
                                    const value = field.value;
                                    if (value === 'N/A' && !['LinkedIn', 'Asset Classes'].includes(field.field)) return '';

                                    let cellContent = value;
                                    let valueClass = field.highlight ? 'font-semibold text-gray-900' : 'text-gray-600';

                                    if (field.email && value !== 'N/A') {
                                        cellContent = `<a href="mailto:${value}" class="text-blue-600 hover:text-blue-800 transition-colors font-medium">${value}</a>`;
                                    } else if (field.link && value !== 'N/A') {
                                        cellContent = `<a href="tel:${value}" class="text-blue-600 hover:text-blue-800 transition-colors">${value}</a>`;
                                    } else if (field.linkedin && value !== 'N/A') {
                                        cellContent = `<a href="https://${value}" target="_blank" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                            View Profile
                                        </a>`;
                                    } else if (field.badge && value !== 'N/A') {
                                        const badgeClass = assetClassColors[value] || 'bg-gray-100 text-gray-800 border-gray-200';
                                        cellContent = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeClass} border">${value}</span>`;
                                    }

                                    return `
                                        <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                                            <span class="font-medium text-gray-700 text-sm">${field.field}</span>
                                            <span class="text-right ${valueClass} text-sm">
                                                ${cellContent}
                                            </span>
                                        </div>
                                    `;
                                }).filter(Boolean).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function displayResults(data) {
            const { company, contacts } = data;

            // Generate placeholder CRM data
            window.crmData = generateCRMPlaceholderData();
            const crmContacts = generateCRMContactPlaceholders();

            // Store data globally
            window.currentContacts = contacts;
            window.currentCompany = company;

            // Update timestamp
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();

            // Populate CRM Account Details
            document.getElementById('crmAccountDetails').innerHTML = createAccountSection(window.crmData, true);

            // Populate Preqin Account Details
            document.getElementById('preqinAccountDetails').innerHTML = createAccountSection(company, false);

            // Populate CRM Contacts
            const crmContactsHTML = crmContacts.map((contact, index) =>
                createContactDropdown(contact, index, true)
            ).join('');
            document.getElementById('crmContactsContainer').innerHTML = crmContactsHTML;

            // Populate Preqin Contacts
            const preqinContactsHTML = contacts.map((contact, index) =>
                createContactDropdown(contact, index, false)
            ).join('');
            document.getElementById('preqinContactsContainer').innerHTML = preqinContactsHTML;

            document.getElementById('searchResults').classList.remove('hidden');
            document.getElementById('searchResults').scrollIntoView({ behavior: 'smooth' });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchQuery').focus();
        });
    </script>
</body>
</html>
