// Content script to handle communication between iframe and extension
window.addEventListener('message', function(event) {
  // Handle messages from iframe requesting to open new tab
  if (event.data && event.data.action === 'openTab') {
    // Send message to background script to open new tab
    chrome.runtime.sendMessage({
      action: 'openComparisonTab',
      url: 'data-comparison.html'
    }, function(response) {
      if (chrome.runtime.lastError) {
        console.error('Error opening tab:', chrome.runtime.lastError);
      } else {
        console.log('Tab opened successfully:', response);
      }
    });
  }
});

// Inject helper function into page context for iframe communication
const script = document.createElement('script');
script.textContent = `
  // Helper function for iframe to request new tab
  window.openExtensionTab = function(url) {
    window.postMessage({
      action: 'openTab',
      url: url
    }, '*');
  };
`;
document.head.appendChild(script);
