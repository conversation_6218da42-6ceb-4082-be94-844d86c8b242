// Background script for Chrome extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'openComparisonTab') {
    // Create new tab with the comparison page
    chrome.tabs.create({
      url: chrome.runtime.getURL(request.url),
      active: true
    }).then((tab) => {
      sendResponse({ success: true, tabId: tab.id });
    }).catch((error) => {
      console.error('Error creating tab:', error);
      sendResponse({ success: false, error: error.message });
    });
    
    // Return true to indicate we will send a response asynchronously
    return true;
  }
});

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('AJAIA CRM Intelligence extension installed');
});
